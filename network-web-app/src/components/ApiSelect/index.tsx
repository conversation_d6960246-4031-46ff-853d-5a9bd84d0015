'use client';

import React from 'react';
import { Controller } from 'react-hook-form';
import { Select } from '@/components';
import { ApiSelectPropsI } from './types';
import useApiSelect from './useHook';

const ApiSelect = ({
  control,
  name,
  label,
  placeholder = 'Select an option',
  isRequired = false,
  disabled = false,
  className = 'w-full',
  searchPlaceholder = 'Search...',
  apiCall,
  optionLabelKey,
  optionValueKey,
  uniqueKey,
  requiredMessage,
  onChangeOption,
  returnIdTypeObject = true, // Default to true for backward compatibility
}: ApiSelectPropsI) => {
  const { options, loading, loadMore, hasMore, onSearch } = useApiSelect({
    apiCall,
    uniqueKey: uniqueKey || optionValueKey,
  });

  return (
    <Controller
      control={control}
      name={name}
      rules={{
        required: isRequired
          ? requiredMessage ||
            `Please select ${label?.toLowerCase() || 'an option'}`
          : false,
      }}
      render={({ field: { onChange, value }, fieldState: { error } }) => {
        const handleOptionChange = (option: any) => {
          if (returnIdTypeObject) {
            // Transform the option to IdTypeI format
            const idTypeValue = {
              id: option[optionValueKey],
              dataType: option.dataType || 'master',
            };
            onChange(idTypeValue);
          } else {
            // Return just the value (for components like CountrySelect)
            onChange(option[optionValueKey]);
          }
          onChangeOption?.(option);
        };

        return (
          <Select
            label={label}
            placeholder={placeholder}
            options={options ?? []}
            value={returnIdTypeObject ? value?.id || value : value} // Handle both string and object values
            onValueChange={selectedId => {
              // Find the full option object
              const selectedOption = options?.find(
                opt => opt[optionValueKey] === selectedId
              );
              if (selectedOption) {
                handleOptionChange(selectedOption);
              }
            }}
            onSearch={onSearch}
            error={error?.message}
            disabled={disabled}
            className={className}
            loading={loading}
            onLoadMore={loadMore}
            hasMore={hasMore}
            searchable={true}
            optionLabelKey={optionLabelKey}
            optionValueKey={optionValueKey}
            searchPlaceholder={searchPlaceholder}
            onChangeOption={handleOptionChange}
          />
        );
      }}
    />
  );
};

export default ApiSelect;
