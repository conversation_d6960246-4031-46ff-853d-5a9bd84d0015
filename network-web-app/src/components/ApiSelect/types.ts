import { Control } from 'react-hook-form';

export type ApiSelectOptionI = {
  [key: string]: any;
};

export type ApiSelectResponseI<T = ApiSelectOptionI> = {
  data: T[];
  total: number;
};

export type ApiSelectParamsI = {
  page: string;
  search?: string;
};

export type ApiSelectPropsI = {
  control: Control<any>;
  name: string;
  label?: string;
  placeholder?: string;
  isRequired?: boolean;
  disabled?: boolean;
  className?: string;
  searchPlaceholder?: string;
  onChangeOption?: (item: any) => void;

  // API configuration
  apiCall: (params: ApiSelectParamsI) => Promise<ApiSelectResponseI>;
  optionLabelKey: string;
  optionValueKey: string;
  uniqueKey?: string; // For deduplication, defaults to optionValueKey

  // Value format control
  returnIdTypeObject?: boolean; // If true, returns {id, dataType}, if false returns just the value

  // Validation
  requiredMessage?: string;
};
