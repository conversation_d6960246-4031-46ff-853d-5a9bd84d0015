'use client';

import React from 'react';
import { ApiSelect } from '@/components';
import type { ApiSelectParamsI, ApiSelectResponseI } from '../ApiSelect/types';
import type { Control } from 'react-hook-form';
import { apiCall } from '@/lib/api';

const fetchFuelTypes = async (
  params: ApiSelectParamsI
): Promise<ApiSelectResponseI> => {
  const query = {
    search: params.search && params.search.trim() !== '' ? params.search : 'a',
    page: Number(params.page) || 0,
    pageSize: 10,
  };
  const response = await apiCall(
    '/backend/api/v1/ship/fuel-type/options',
    'GET',
    {
      isAuth: true,
      query,
    }
  );
  return response as ApiSelectResponseI;
};

export type FuelTypeSelectPropsI = {
  control: Control<any>;
  name: string;
  label?: string;
  placeholder?: string;
  isRequired?: boolean;
  disabled?: boolean;
  className?: string;
  onChange?: (item: any) => void;
};

const FuelTypeSelect = ({
  control,
  name,
  label = 'Fuel Type',
  placeholder = 'Select fuel type',
  isRequired = false,
  disabled = false,
  className = 'w-full',
  onChange,
}: FuelTypeSelectPropsI) => {
  return (
    <ApiSelect
      control={control}
      name={name}
      label={label}
      placeholder={placeholder}
      isRequired={isRequired}
      disabled={disabled}
      className={className}
      searchPlaceholder="Search fuel types..."
      apiCall={fetchFuelTypes}
      onChangeOption={onChange}
      optionLabelKey="name"
      optionValueKey="id"
      uniqueKey="id"
      requiredMessage="Please select fuel type"
    />
  );
};

export default FuelTypeSelect;
