import { FilterItemI, FilterTabI } from '@/networks/jobs/types';

export type JobFilterBarPropsI = {
  search: string;
  onSearchChange: (value: string) => void;
  isFilterModalOpen: boolean;
  onOpenFilterModal: () => void;
  onCloseFilterModal: () => void;
  onApplyFilters: (filters: Record<string, any>) => void;
  onClearAllFilters: () => void;
  filterTabs: FilterTabI[];
  filterData: Record<string, FilterItemI[]>;
  currentFilters: Record<string, FilterItemI[]>;
  filterLoading: boolean;
  hasActiveFilters: boolean;
  activeFilterCount: number;
  isApplyingFilters?: boolean;
  className?: string;
};
