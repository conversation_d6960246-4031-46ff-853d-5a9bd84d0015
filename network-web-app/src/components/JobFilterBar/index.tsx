'use client';

import React, { useRef } from 'react';
import { SearchInput } from '@/components';
import FilterModal from '@/components/FilterModal';
import Filter from '@assets/svg/Filter';
import { JobFilterBarPropsI } from './types';

const JobFilterBar: React.FC<JobFilterBarPropsI> = ({
  search,
  onSearchChange,
  isFilterModalOpen,
  onOpenFilterModal,
  onCloseFilterModal,
  onApplyFilters,
  onClearAllFilters,
  filterTabs,
  filterData,
  currentFilters,
  filterLoading,
  hasActiveFilters,
  activeFilterCount,
  isApplyingFilters = false,
  className = '',
}) => {
  const filterButtonRef = useRef<HTMLButtonElement>(null);

  return (
    <div className={`px-4 py-3 ${className}`}>
      <SearchInput
        value={search}
        onChange={e => onSearchChange(e.target.value)}
        rightSlot={
          <div className="flex items-center gap-2 relative">
            <button
              ref={filterButtonRef}
              onClick={onOpenFilterModal}
              disabled={isApplyingFilters}
              className="relative p-2 rounded-md hover:bg-gray-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              title="Filter jobs"
            >
              {isApplyingFilters ? (
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-gray-600"></div>
              ) : (
                <Filter />
              )}
              {hasActiveFilters && !isApplyingFilters && (
                <span className="absolute -top-1 -right-1 inline-flex items-center justify-center px-1.5 py-0.5 text-xs font-bold leading-none text-white bg-primary rounded-full min-w-[18px]">
                  {activeFilterCount}
                </span>
              )}
            </button>
            {hasActiveFilters && (
              <button
                onClick={onClearAllFilters}
                disabled={isApplyingFilters}
                className="text-xs text-gray-500 hover:text-gray-700 px-2 py-1 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                title="Clear all filters"
              >
                {isApplyingFilters ? 'Clearing...' : 'Clear'}
              </button>
            )}

            <FilterModal
              isOpen={isFilterModalOpen}
              onClose={onCloseFilterModal}
              onApply={onApplyFilters}
              filterTabs={filterTabs}
              filterData={filterData}
              currentFilters={currentFilters}
              loading={filterLoading}
              triggerRef={filterButtonRef}
            />
          </div>
        }
      />
    </div>
  );
};

export default JobFilterBar;
