import { apiCall } from '@/lib/api';
import {
  FetchJobsQueryI,
  FetchJobsResponseI,
  FetchJobsForApplicantQueryI,
  FetchJobsForApplicantResponseI,
} from './types';

export const fetchJobsForCandidateAPI = async (
  query: FetchJobsQueryI,
  payload?: Partial<FetchJobsForCandidateBodyI>
): Promise<FetchJobsResponseI> => {
  const defaultPayload: FetchJobsForCandidateBodyI = {
    designations: null,
    countries: [],
    shipTypes: null,
    internetLimits: [],
  };

  const result = await apiCall<
    FetchJobsQueryI | FetchJobsForCandidateBodyI,
    FetchJobsResponseI
  >('/backend/api/v1/company/job/candidate', 'POST', {
    isAuth: true,
    query,
    payload: { ...defaultPayload, ...payload },
  });
  return result;
};

export const fetchJobsForEntityMemberAPI = async (
  query: FetchJobsQueryI,
  payload: FetchJobsForEntityMemberPayloadI
): Promise<FetchJobsResponseI> => {
  const result = await apiCall<
    FetchJobsQueryI | FetchJobsForEntityMemberPayloadI,
    FetchJobsResponseI
  >('/backend/api/v1/company/job/entity-member', 'POST', {
    isAuth: true,
    query,
    payload,
  });
  return result;
};

import type {
  JobCandidateFetchOneResultI,
  FetchApplicantsQueryI,
  FetchApplicantsBodyI,
  FetchApplicantsResponseI,
  FetchJobsForApplicantBodyI,
  FetchJobsForCandidateBodyI,
  FetchJobsForEntityMemberPayloadI,
  JobFiltersI,
  FetchFiltersQueryI,
  FetchFiltersForEntityMemberQueryI,
  FetchFiltersForEntityMemberBodyI,
  ApplicantsStatusI,
} from './types';

export const fetchJobDetailAPI = async (
  jobId: string
): Promise<JobCandidateFetchOneResultI> => {
  const result = await apiCall<string, JobCandidateFetchOneResultI>(
    `/backend/api/v1/company/job/candidate/${jobId}`,
    'GET',
    { isAuth: true }
  );
  console.log(`Fetched Job Details: ${JSON.stringify(result)}`);
  return result;
};

export const closeJobAPI = async (jobId: string): Promise<void> => {
  await apiCall<unknown, unknown>(
    `/backend/api/v1/company/job/core/${jobId}`,
    'PATCH',
    {
      isAuth: true,
      payload: { expiryDate: new Date().toISOString() },
    }
  );
};

export const fetchApplicantsForJobAPI = async (
  query: FetchApplicantsQueryI,
  payload: FetchApplicantsBodyI = {}
): Promise<FetchApplicantsResponseI> => {
  const result = await apiCall<FetchApplicantsBodyI, FetchApplicantsResponseI>(
    '/backend/api/v1/company/job/applications/entity-member',
    'POST',
    {
      isAuth: true,
      query,
      payload,
    }
  );
  return result;
};

export const fetchJobsForApplicantAPI = async (
  query: FetchJobsForApplicantQueryI,
  payload?: Partial<FetchJobsForApplicantBodyI>
): Promise<FetchJobsForApplicantResponseI> => {
  const defaultPayload: FetchJobsForApplicantBodyI = {
    countries: [],
    designations: null,
    shipTypes: null,
    internetLimits: [],
  };

  const result = await apiCall<
    FetchJobsForApplicantQueryI | FetchJobsForApplicantBodyI,
    FetchJobsForApplicantResponseI
  >('/backend/api/v1/company/jobs/application/applicant', 'POST', {
    isAuth: true,
    query,
    payload: { ...defaultPayload, ...payload },
  });
  return result;
};

export const fetchFiltersForEntityMemberAPI = async (
  query: FetchFiltersForEntityMemberQueryI,
  payload?: Partial<FetchFiltersForEntityMemberBodyI>
): Promise<JobFiltersI> => {
  const defaultPayload: FetchFiltersForEntityMemberBodyI = {
    entity: null,
  };

  const result = await apiCall<
    FetchFiltersForEntityMemberQueryI | FetchFiltersForEntityMemberBodyI,
    JobFiltersI
  >('/backend/api/v1/company/jobs/entity-member/filters', 'POST', {
    isAuth: true,
    query,
    payload: { ...defaultPayload, ...payload },
  });
  return result;
};

// Filter APIs
export const fetchFiltersForCandidateAPI = async (): Promise<JobFiltersI> => {
  const result = await apiCall<unknown, JobFiltersI>(
    '/backend/api/v1/company/jobs/candidate/filters',
    'GET',
    {
      isAuth: true,
      query: {
        isOfficial: false,
      },
    }
  );
  return result;
};

export const fetchFiltersForApplicantAPI = async (
  query: FetchFiltersQueryI
): Promise<JobFiltersI> => {
  const result = await apiCall<any, any>(
    '/backend/api/v1/company/job/entity-member/filters',
    'GET',
    {
      isAuth: true,
      query,
    }
  );
  return result;
};

export const fetchFiltersForApplicantsAPI = async (query: {
  jobId: string;
  status?: ApplicantsStatusI;
}): Promise<any> => {
  const result = await apiCall<any, any>(
    '/backend/api/v1/company/job/entity-member/filters',
    'GET',
    {
      isAuth: true,
      query,
    }
  );
  return result;
};

export const updateApplicationStatusAPI = async (
  applicationId: string,
  status: 'SHORTLISTED' | 'REJECTED_BY_ENTITY' | 'OFFERED'
): Promise<void> => {
  const payload = {
    applicationId,
    status,
  };

  await apiCall<typeof payload, void>(
    `/backend/api/v1/company/job/application/entity-member/${applicationId}`,
    'PATCH',
    {
      isAuth: true,
      payload,
    }
  );
};

export const deleteJobAPI = async (jobId: string): Promise<void> => {
  await apiCall<unknown, void>(
    `/backend/api/v1/company/job/status/${jobId}`,
    'PATCH',
    {
      isAuth: true,
    }
  );
};
