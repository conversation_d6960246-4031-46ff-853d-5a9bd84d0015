'use client';

import React, { useState } from 'react';
import { Controller, useFieldArray, useFormContext } from 'react-hook-form';
import {
  Button,
  Input,
  Textarea,
  Toggle,
  CertificationSelect,
  DocumentsSelect,
  MultipleCountrySelect,
  DesignationSelect,
  ShipTypeSelect,
  SkillSelect,
  EquipmentTypeSelect,
  EquipmentMakeSelect,
  EquipmentModelSelect,
  FuelTypeSelect,
} from '@/components';
import { JobFormDataI } from '../PostJobForm/types';
import TrashIcon from '@assets/svg/Trash';

const JobsRequirements: React.FC<{ onNext: (isAdvanced: boolean) => void }> = ({
  onNext,
}) => {
  const { control } = useFormContext<JobFormDataI>();
  const [isAdvanced, setIsAdvanced] = useState(true);

  const certFA = useFieldArray({ control, name: 'certifications' });
  const docFA = useFieldArray({ control, name: 'documents' });
  const expFA = useFieldArray({ control, name: 'experiences' });
  const eqFA = useFieldArray({ control, name: 'equipments' });
  const cargoFA = useFieldArray({ control, name: 'cargos' });
  const skillFA = useFieldArray({ control, name: 'skills' });
  const otherFA = useFieldArray({ control, name: 'otherRequirements' });

  return (
    <div className=" rounded-xl  space-y-8">
      <div className="flex items-center justify-between mb-3">
        <p className="text-[#959090] font-medium">Step 2/3 : Requirements</p>
        <Toggle
          checked={isAdvanced}
          onChange={setIsAdvanced}
          label="Advanced requirements"
        />
      </div>

      {/* Textareas */}
      <Controller
        control={control}
        name="about"
        render={({ field }) => (
          <Textarea
            {...field}
            label="About"
            placeholder="Enter about"
            rows={4}
          />
        )}
      />

      <Controller
        control={control}
        name="roles"
        render={({ field }) => (
          <Textarea
            {...field}
            label="Roles and responsibilities"
            placeholder="Enter roles and responsibilities"
            rows={4}
          />
        )}
      />

      {!isAdvanced && (
        <Controller
          control={control}
          name="benefits"
          render={({ field }) => (
            <Textarea
              {...field}
              label="Benefits"
              placeholder="Enter benefits"
              rows={4}
            />
          )}
        />
      )}

      {/* Certification */}
      {isAdvanced && (
        <>
          <section>
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-semibold text-gray-900">Certification</h3>
            </div>
            <div className="space-y-3">
              {certFA.fields.map((f, idx) => (
                <div
                  key={f.id}
                  className="bg-gray-50 rounded-xl p-3 md:p-4 grid grid-cols-1 md:grid-cols-12 gap-3 items-end"
                >
                  <div className="md:col-span-5">
                    <CertificationSelect
                      control={control}
                      name={`certifications.${idx}.certification`}
                      label={`Certification ${idx + 1}`}
                      placeholder="Select certification"
                    />
                  </div>

                  <div className="md:col-span-2 flex items-center justify-between md:justify-around">
                    <Controller
                      control={control}
                      name={`certifications.${idx}.mandatory` as const}
                      render={({ field }) => (
                        <Toggle
                          checked={!!field.value}
                          onChange={field.onChange}
                          label="Mandatory"
                        />
                      )}
                    />
                    <div
                      onClick={() => certFA.remove(idx)}
                      className="cursor-pointer"
                    >
                      <TrashIcon size={24} />
                    </div>
                  </div>
                </div>
              ))}
              <button
                type="button"
                onClick={() => certFA.append({})}
                className="text-primary hover:underline font-medium"
              >
                Add new certification
              </button>
            </div>
          </section>

          <section>
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-semibold text-gray-900">Documents</h3>
            </div>
            <div className="space-y-3">
              {docFA.fields.map((f, idx) => (
                <div
                  key={f.id}
                  className="bg-gray-50 rounded-xl p-3 md:p-4 grid grid-cols-1 md:grid-cols-12 gap-3 items-end"
                >
                  <div className="md:col-span-5">
                    <DocumentsSelect
                      control={control}
                      name={`documents.${idx}.documentType`}
                      label={`Document ${idx + 1}`}
                      placeholder="Select document"
                    />
                  </div>
                  <div className="md:col-span-5">
                    <MultipleCountrySelect
                      control={control}
                      name={`documents.${idx}.countries`}
                      label="Countries"
                      placeholder="Select countries"
                    />
                  </div>
                  <div className="md:col-span-2 flex items-center justify-between md:justify-around">
                    <Controller
                      control={control}
                      name={`documents.${idx}.mandatory`}
                      render={({ field }) => (
                        <Toggle
                          checked={!!field.value}
                          onChange={field.onChange}
                          label="Mandatory"
                        />
                      )}
                    />
                    <div
                      onClick={() => docFA.remove(idx)}
                      className="cursor-pointer"
                    >
                      <TrashIcon size={24} />
                    </div>
                  </div>
                </div>
              ))}
              <button
                type="button"
                onClick={() => docFA.append({})}
                className="text-primary hover:underline font-medium"
              >
                Add new document
              </button>
            </div>
          </section>

          {/* Experience */}
          <section>
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-semibold text-gray-900">Experience</h3>
            </div>
            <div className="space-y-3">
              {expFA.fields.map((f, idx) => (
                <div
                  key={f.id}
                  className="bg-gray-50 rounded-xl p-3 md:p-4 grid grid-cols-1 md:grid-cols-12 gap-3 items-end"
                >
                  <div className="md:col-span-3">
                    <DesignationSelect
                      control={control}
                      name={`experiences.${idx}.designation`}
                      label={`Designation ${idx + 1}`}
                      placeholder="Select designation"
                    />
                  </div>
                  <div className="md:col-span-3">
                    <ShipTypeSelect
                      control={control}
                      name={`experiences.${idx}.shipType`}
                      label="Ship type"
                      placeholder="Select type"
                    />
                  </div>
                  <div className="md:col-span-3">
                    <Controller
                      control={control}
                      name={`experiences.${idx}.months` as const}
                      render={({ field }) => (
                        <Input
                          {...field}
                          label="Months of experience (MOE)"
                          placeholder="Enter months"
                          type="number"
                        />
                      )}
                    />
                  </div>
                  <div className="md:col-span-3 flex items-center justify-between md:justify-around">
                    <Controller
                      control={control}
                      name={`experiences.${idx}.mandatory` as const}
                      render={({ field }) => (
                        <Toggle
                          checked={!!field.value}
                          onChange={field.onChange}
                          label="Mandatory"
                        />
                      )}
                    />
                    <div
                      onClick={() => expFA.remove(idx)}
                      className="cursor-pointer"
                    >
                      <TrashIcon size={24} />
                    </div>
                  </div>
                </div>
              ))}
              <button
                type="button"
                onClick={() => expFA.append({})}
                className="text-primary hover:underline font-medium"
              >
                Add new experience
              </button>
            </div>
          </section>

          <section>
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-semibold text-gray-900">Equipment</h3>
            </div>
            <div className="space-y-3">
              {eqFA.fields.map((f, idx) => (
                <div
                  key={f.id}
                  className="bg-gray-50 rounded-xl p-3 md:p-4 grid grid-cols-1 md:grid-cols-12 gap-3 items-end"
                >
                  <div className="md:col-span-2">
                    <EquipmentTypeSelect
                      control={control}
                      name={`equipments.${idx}.type`}
                      label="Type"
                      placeholder="Select type"
                    />
                  </div>
                  <div className="md:col-span-2">
                    <EquipmentMakeSelect
                      control={control}
                      name={`equipments.${idx}.make`}
                      label="Make"
                      placeholder="Select make"
                    />
                  </div>
                  <div className="md:col-span-2">
                    <EquipmentModelSelect
                      control={control}
                      name={`equipments.${idx}.model`}
                      label="Model"
                      placeholder="Select model"
                    />
                  </div>
                  <div className="md:col-span-2">
                    <FuelTypeSelect
                      control={control}
                      name={`equipments.${idx}.fuelType`}
                      label="Fuel Type"
                      placeholder="Select fuel type"
                    />
                  </div>
                  <div className="md:col-span-2">
                    <Controller
                      control={control}
                      name={`equipments.${idx}.moe` as const}
                      render={({ field }) => (
                        <Input
                          {...field}
                          label="MOE"
                          placeholder="Enter MOE"
                          type="number"
                        />
                      )}
                    />
                  </div>
                  <div className="md:col-span-2 flex items-center justify-between md:justify-around">
                    <Controller
                      control={control}
                      name={`equipments.${idx}.mandatory` as const}
                      render={({ field }) => (
                        <Toggle
                          checked={!!field.value}
                          onChange={field.onChange}
                          label="Mandatory"
                        />
                      )}
                    />
                    <div
                      onClick={() => eqFA.remove(idx)}
                      className="cursor-pointer"
                    >
                      <TrashIcon size={24} />
                    </div>
                  </div>
                </div>
              ))}
              <button
                type="button"
                onClick={() => eqFA.append({})}
                className="text-primary hover:underline font-medium"
              >
                Add new equipment
              </button>
            </div>
          </section>

          {/* Cargo */}
          <section>
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-semibold text-gray-900">Cargo</h3>
            </div>
            <div className="space-y-3">
              {cargoFA.fields.map((f, idx) => (
                <div
                  key={f.id}
                  className="bg-gray-50 rounded-xl p-3 md:p-4 grid grid-cols-1 md:grid-cols-12 gap-3 items-end"
                >
                  <div className="md:col-span-4">
                    <Controller
                      control={control}
                      name={`cargos.${idx}.name` as const}
                      render={({ field }) => (
                        <Input
                          {...field}
                          label="Name"
                          placeholder="Enter name"
                        />
                      )}
                    />
                  </div>
                  <div className="md:col-span-3">
                    <Controller
                      control={control}
                      name={`cargos.${idx}.code` as const}
                      render={({ field }) => (
                        <Input
                          {...field}
                          label="Code"
                          placeholder="Enter code"
                        />
                      )}
                    />
                  </div>
                  <div className="md:col-span-2">
                    <Controller
                      control={control}
                      name={`cargos.${idx}.months` as const}
                      render={({ field }) => (
                        <Input
                          {...field}
                          label="Months of experience (MOE)"
                          placeholder="Enter months"
                          type="number"
                        />
                      )}
                    />
                  </div>
                  <div className="md:col-span-3 flex items-center justify-between md:justify-around">
                    <Controller
                      control={control}
                      name={`cargos.${idx}.mandatory` as const}
                      render={({ field }) => (
                        <Toggle
                          checked={!!field.value}
                          onChange={field.onChange}
                          label="Mandatory"
                        />
                      )}
                    />
                    <div
                      onClick={() => cargoFA.remove(idx)}
                      className="cursor-pointer"
                    >
                      <TrashIcon size={24} />
                    </div>
                  </div>
                </div>
              ))}
              <button
                type="button"
                onClick={() => cargoFA.append({})}
                className="text-primary hover:underline font-medium"
              >
                Add new cargo
              </button>
            </div>
          </section>

          {/* Skills */}
          <section>
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-semibold text-gray-900">Skills</h3>
            </div>
            <div className="space-y-3">
              {skillFA.fields.map((f, idx) => (
                <div
                  key={f.id}
                  className="bg-gray-50 rounded-xl p-3 md:p-4 grid grid-cols-1 md:grid-cols-12 gap-3 items-end"
                >
                  <div className="md:col-span-10">
                    <SkillSelect
                      control={control}
                      name={`skills.${idx}.name`}
                      label="Skill"
                      placeholder="Select skill"
                    />
                  </div>
                  <div className="md:col-span-2 flex items-center justify-between md:justify-around">
                    <Controller
                      control={control}
                      name={`skills.${idx}.mandatory` as const}
                      render={({ field }) => (
                        <Toggle
                          checked={!!field.value}
                          onChange={field.onChange}
                          label="Mandatory"
                        />
                      )}
                    />
                    <div
                      onClick={() => skillFA.remove(idx)}
                      className="cursor-pointer"
                    >
                      <TrashIcon size={24} />
                    </div>
                  </div>
                </div>
              ))}
              <button
                type="button"
                onClick={() => skillFA.append({})}
                className="text-primary hover:underline font-medium"
              >
                Add new skill
              </button>
            </div>
          </section>

          {/* Other requirements */}
          <section>
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-semibold text-gray-900">
                Other requirements
              </h3>
            </div>
            <div className="space-y-3">
              {otherFA.fields.map((f, idx) => (
                <div
                  key={f.id}
                  className="bg-gray-50 rounded-xl p-3 md:p-4 grid grid-cols-1 md:grid-cols-12 gap-3 items-end"
                >
                  <div className="md:col-span-2">
                    <Controller
                      control={control}
                      name={`otherRequirements.${idx}.no` as const}
                      render={({ field }) => (
                        <Input
                          {...field}
                          label="No"
                          placeholder="Auto"
                          disabled
                          value={String(idx + 1)}
                        />
                      )}
                    />
                  </div>
                  <div className="md:col-span-8">
                    <Controller
                      control={control}
                      name={`otherRequirements.${idx}.details` as const}
                      render={({ field }) => (
                        <Input
                          {...field}
                          label="Details"
                          placeholder="Enter details"
                        />
                      )}
                    />
                  </div>
                  <div className="md:col-span-2 flex items-center justify-between md:justify-around">
                    <Controller
                      control={control}
                      name={`otherRequirements.${idx}.mandatory` as const}
                      render={({ field }) => (
                        <Toggle
                          checked={!!field.value}
                          onChange={field.onChange}
                          label="Mandatory"
                        />
                      )}
                    />
                    <div
                      onClick={() => otherFA.remove(idx)}
                      className="cursor-pointer"
                    >
                      <TrashIcon size={24} />
                    </div>
                  </div>
                </div>
              ))}
              <button
                type="button"
                onClick={() => otherFA.append({})}
                className="text-primary hover:underline font-medium"
              >
                Add new requirement
              </button>
            </div>
          </section>
        </>
      )}
      <div className="pt-2">
        <Button
          type="button"
          className="bg-green-700 hover:bg-green-800 text-white"
          onClick={() => onNext(isAdvanced)}
        >
          Generate benefits
        </Button>
      </div>
    </div>
  );
};

export default JobsRequirements;
