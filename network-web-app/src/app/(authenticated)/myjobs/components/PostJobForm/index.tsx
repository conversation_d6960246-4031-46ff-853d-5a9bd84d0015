'use client';

import { Controller, FormProvider } from 'react-hook-form';
import {
  Button,
  Input,
  DashboardLayout,
  OrganizationSelect,
  DepartmentSelect,
  JobTypeSelect,
  ShipSelect,
  ShipTypeSelect,
} from '@/components';
import CountrySelect from '@/components/CountrySelect';
import DesignationSelect from '@/components/DesignationSelect';
import { Select } from '@/components/Select';
import { Toggle } from '@/components/Toggle';
import InfoIcon from '@assets/svg/Info';
import CenterLayout from '../CenterLayout';
import JobsRequirements from '../JobRequirements';
import JobBenefits from '../JobBenefits';
import usePostJobForm from './useHook';

const PostJobForm: React.FC = () => {
  const {
    onSubmit,
    methods,
    step,
    isSubmitting,
    isSubmittingRequirements,
    submitRequirements,
    isSubmittingBenefits,
    submitBenefits,
    handleBack,
    applicationTypesOptions,
  } = usePostJobForm();
  const { control, handleSubmit, watch } = methods;
  const gdi = watch('genderDiversityIndex');

  const Layout = step === 1 ? DashboardLayout : CenterLayout;

  return (
    <Layout {...(step === 1 ? { hideSwitchProfile: true } : {})}>
      <section className="lg:col-span-6 col-span-1">
        <div className="bg-white  rounded-2xl">
          <div className="flex justify-between">
            <h1 className="text-2xl font-semibold text-gray-900 mb-2">
              Post new job
            </h1>
          </div>

          <FormProvider {...methods}>
            {step === 1 && (
              <div className="border border-gray-200 rounded-xl p-4 md:p-6">
                <p className="text-[#959090] font-medium mb-2">
                  Step 1/3 : Job details
                </p>

                <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-1 gap-4">
                    <OrganizationSelect
                      control={control}
                      name="entity"
                      label="Company"
                      placeholder="Search company"
                      isRequired
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <ShipSelect
                      control={control}
                      name="shipImo"
                      label="Ship IMO"
                      placeholder="Search ship by name/IMO"
                      onChange={opt => {
                        if (opt?.imo && opt?.dataType) {
                          methods.setValue('ship', {
                            imo: opt.imo,
                            dataType: opt.dataType,
                          });
                          methods.setValue('shipImo', opt.imo);
                        }
                      }}
                    />

                    <ShipTypeSelect
                      control={control}
                      name="shipType"
                      label="Ship type"
                      placeholder="Select ship type"
                    />

                    <Controller
                      control={control}
                      name="showShipDetails"
                      render={({ field }) => (
                        <Toggle
                          checked={field.value}
                          onChange={field.onChange}
                          label="Show Ship Details"
                          className="mt-6"
                        />
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <JobTypeSelect
                      control={control}
                      name="jobType"
                      label="Job type"
                      placeholder="Select"
                      isRequired
                    />

                    <DesignationSelect
                      control={control}
                      name="designation"
                      label="Designation"
                      placeholder="Select"
                      isRequired
                    />

                    <CountrySelect
                      control={control}
                      name="locationCountryIso2"
                      label="Location"
                      placeholder="Select"
                      isRequired
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-center">
                    <DepartmentSelect
                      control={control}
                      name="department"
                      label="Department"
                      placeholder="Select"
                      isRequired
                    />

                    <Controller
                      control={control}
                      name="expiryDate"
                      rules={{ required: 'Expiry date is required' }}
                      render={({ field, fieldState }) => (
                        <Input
                          {...field}
                          type="date"
                          label="Expiry Date"
                          placeholder="YYYY-MM-DD"
                          error={fieldState.error?.message}
                        />
                      )}
                    />
                    <Controller
                      control={control}
                      name="joiningDate"
                      render={({ field, fieldState }) => (
                        <Input
                          {...field}
                          type="date"
                          label="Joining Date"
                          placeholder="YYYY-MM-DD"
                          error={fieldState.error?.message}
                        />
                      )}
                    />
                  </div>

                  {/* Application Type */}
                  <div className="grid grid-cols-2 md:grid-cols-2 gap-4 items-center">
                    <Controller
                      control={control}
                      name="applicationType"
                      render={({ field }) => (
                        <Select
                          label="Application Type"
                          placeholder="Select type"
                          options={applicationTypesOptions}
                          value={field.value}
                          onValueChange={field.onChange}
                        />
                      )}
                    />

                    <Controller
                      control={control}
                      name="applicationType"
                      render={({ field: { value } }) => (
                        <div className="">
                          {value === 'link' && (
                            <Controller
                              control={control}
                              name="applicationLink"
                              rules={{
                                required: 'Application Link is required',
                                validate: (v?: string) =>
                                  value === 'link'
                                    ? (!!v && v.trim() !== '') ||
                                      'Application Link is required'
                                    : true,
                              }}
                              render={({ field, fieldState }) => (
                                <Input
                                  {...field}
                                  type="url"
                                  label="Application Link"
                                  placeholder="https://example.com/apply"
                                  error={fieldState.error?.message}
                                />
                              )}
                            />
                          )}

                          {value === 'email' && (
                            <Controller
                              control={control}
                              name="applicationEmail"
                              rules={{
                                required: 'Application Email is required',
                                pattern: {
                                  value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                                  message: 'Please enter a valid email',
                                },
                              }}
                              render={({ field, fieldState }) => (
                                <Input
                                  {...field}
                                  type="email"
                                  label="Application Email"
                                  placeholder="<EMAIL>"
                                  error={fieldState.error?.message}
                                />
                              )}
                            />
                          )}
                        </div>
                      )}
                    />
                  </div>

                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center">
                        <label className="text-lg md:text-xl font-semibold text-gray-900">
                          Gender Diversity Index
                        </label>
                        <InfoIcon />
                      </div>

                      <div className="text-gray-600 font-medium bg-gray-100 px-3 py-1 rounded-md">
                        {gdi?.toFixed(2)}/1
                      </div>
                    </div>

                    <Controller
                      control={control}
                      name="genderDiversityIndex"
                      rules={{ min: 0, max: 1 }}
                      render={({ field }) => (
                        <input
                          type="range"
                          min={0}
                          max={1}
                          step={0.01}
                          value={field.value}
                          onChange={e => field.onChange(Number(e.target.value))}
                          className="w-full custom-slider cursor-pointer"
                          style={
                            {
                              '--slider-value': field.value,
                            } as React.CSSProperties
                          }
                        />
                      )}
                    />
                  </div>

                  <div className="pt-2 grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="flex items-end">
                      <Button
                        type="submit"
                        className="text-white w-full md:w-auto bg-green-700 hover:bg-green-800"
                        disabled={isSubmitting}
                      >
                        {isSubmitting ? 'Saving...' : 'Proceed to step 2'}
                      </Button>
                    </div>
                  </div>
                </form>
              </div>
            )}

            {step === 2 && (
              <div aria-busy={isSubmittingRequirements || undefined}>
                <JobsRequirements
                  onNext={isAdvanced => submitRequirements(isAdvanced)}
                />
              </div>
            )}

            {step === 3 && (
              <form
                aria-busy={isSubmittingBenefits || undefined}
                onSubmit={e => {
                  e.preventDefault();
                  submitBenefits();
                }}
              >
                <JobBenefits
                  onSubmit={() => submitBenefits()}
                  onBack={handleBack}
                />
              </form>
            )}
          </FormProvider>
        </div>
      </section>
    </Layout>
  );
};

export default PostJobForm;
