import React, { useCallback, useEffect, useState } from 'react';
import { Button } from '@/components';
import Accordion from '@/components/Accordion';
import { JobDetailSkeleton } from './Shimmer';
import { fetchJobDetailAPI } from '@/networks/jobs';
import type {
  JobCandidateFetchOneResultI,
  BenefitsI,
  CertificationRequirementsI,
  DocumentRequirementsI,
  CargosI,
  SkillRequirementsI,
  OtherRequirementsI,
  EquipmentRequirementsI,
  ExperienceRequirementsI,
} from '@/networks/jobs/types';

export type JobDetailPropsI = {
  jobId: string;
};

const JobDetail: React.FC<JobDetailPropsI> = ({ jobId }) => {
  const [job, setJob] = useState<JobCandidateFetchOneResultI | null>(null);
  const [loading, setLoading] = useState(false);
  const [applying, setApplying] = useState(false);

  const load = useCallback(async () => {
    try {
      setLoading(true);
      const res = await fetchJobDetailAPI(jobId);
      setJob(res);
    } catch (e) {
      console.error('Failed to load job', e);
    } finally {
      setLoading(false);
    }
  }, [jobId]);

  useEffect(() => {
    load();
  }, [load]);

  const onApplyJob = async () => {
    try {
      if (!job?.id) return;
      setApplying(true);
      console.log('Applying to job:', job.id);
      await load();
    } catch (e) {
      console.error('Failed to apply to job', e);
    } finally {
      setApplying(false);
    }
  };

  const formatElapsedTime = (dateString: string) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return '1 day ago';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
    return `${Math.floor(diffDays / 30)} months ago`;
  };

  const title = job?.designation?.name || 'Job';
  const companyName = job?.entity?.name || '—';
  const createdAt = job?.createdAt ? formatElapsedTime(job.createdAt) : '';
  const matchingPercentage = job?.matching || 0;
  const applicationStatus = job?.applicationStatus;
  const hasApplied =
    applicationStatus === 'PENDING' ||
    applicationStatus === 'SHORTLISTED' ||
    applicationStatus === 'OFFERED' ||
    applicationStatus === 'ACCEPTED_BY_APPLICANT';

  const locationText = job?.countryName || 'Not specified';
  const aboutText = job?.about || 'Not specified';
  const rolesResponsibilitiesText =
    job?.rolesResponsibilities || 'Not specified';
  const requirementsText = job?.requirements;
  const benefitsText = job?.benefits;
  const jobType = job?.jobType || '';
  const shipType = job?.shipType?.name || '';
  const shipImo = job?.ship?.imo || '';
  const genderEquityIndex = job?.genderDiversityIndex || '';
  const showShipDetails = job?.showShipDetails || false;

  const certificates = job?.certificationRequirements || [];
  const documents = job?.documentRequirements || [];
  const experiences = job?.experienceRequirements || [];
  const equipments = job?.equipmentRequirements || [];
  const cargos = job?.cargoRequirements || [];
  const skills = job?.skillRequirements || [];
  const otherRequirements = job?.otherRequirements || [];

  const benefits: BenefitsI | undefined =
    job?.benefitType === 'BASIC'
      ? undefined
      : {
          internetDetails: {
            internetAvailable: job?.internetAvailable ?? true,
            internetSpeed: job?.internetSpeed ?? 0,
            internetLimit: job?.internetLimitPerDay ?? 0,
            details: job?.internetDetails ?? 'nil',
          },
          insuranceDetails: {
            insuranceType: job?.insuranceType ?? 'nil',
            itfType: job?.itfType ?? 'nil',
            familyOnboard: job?.familyOnboard ?? null,
          },
          salaryDetails: {
            showSalary: job?.showSalary ?? false,
            maxSalary: job?.maxSalary ?? 0,
            minSalary: job?.minSalary ?? 0,
            salaryType: job?.salaryType ?? 'nil',
            currencyCode: job?.currencyCode ?? 'nil',
          },
          contractDetails: {
            contractDays: job?.contractDays,
            contractMonths: job?.contractMonths,
          },
        };

  const showAdvancedFields =
    certificates.length > 0 ||
    documents.length > 0 ||
    experiences.length > 0 ||
    equipments.length > 0 ||
    cargos.length > 0 ||
    skills.length > 0 ||
    otherRequirements.length > 0 ||
    (benefits &&
      (benefits.internetDetails ||
        benefits.insuranceDetails ||
        benefits.salaryDetails ||
        benefits.contractDetails));

  if (loading) {
    return <JobDetailSkeleton />;
  }

  return (
    <section className="lg:col-span-6 col-span-1" data-job-id={jobId}>
      <div className="bg-white border border-gray-200 rounded-2xl shadow-sm">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-start justify-between gap-4">
            <div>
              <div className="flex items-center text-gray-600 text-sm">
                <span className="inline-block h-4 w-4 rounded-full bg-gray-200 mr-2" />
                {companyName}
              </div>
              <h1 className="mt-1 text-2xl font-semibold text-gray-900">
                {title}
              </h1>
              <div className="mt-1 flex items-center gap-2 text-sm">
                <span className="text-gray-600">{locationText}</span>
                <span className="text-gray-400">•</span>
                <span className="text-gray-600">{createdAt}</span>
                {matchingPercentage > 0 && (
                  <>
                    <span className="text-gray-400">•</span>
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-semibold bg-gray-100 text-gray-800 border border-gray-300">
                      🎯 {matchingPercentage}% match
                    </span>
                  </>
                )}
              </div>
              {applicationStatus && (
                <div className="mt-2">
                  <span
                    className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      applicationStatus === 'PENDING'
                        ? 'bg-yellow-100 text-yellow-800'
                        : applicationStatus === 'SHORTLISTED'
                          ? 'bg-blue-100 text-blue-800'
                          : applicationStatus === 'OFFERED'
                            ? 'bg-purple-100 text-purple-800'
                            : applicationStatus === 'ACCEPTED_BY_APPLICANT'
                              ? 'bg-green-100 text-green-800'
                              : applicationStatus === 'REJECTED_BY_ENTITY'
                                ? 'bg-red-100 text-red-800'
                                : 'bg-gray-100 text-gray-800'
                    }`}
                  >
                    {applicationStatus === 'PENDING'
                      ? 'Applied'
                      : applicationStatus === 'SHORTLISTED'
                        ? 'Shortlisted'
                        : applicationStatus === 'OFFERED'
                          ? 'Offered'
                          : applicationStatus === 'ACCEPTED_BY_APPLICANT'
                            ? 'Accepted'
                            : applicationStatus === 'REJECTED_BY_ENTITY'
                              ? 'Rejected'
                              : applicationStatus}
                  </span>
                </div>
              )}
            </div>
            <button
              type="button"
              aria-label="More options"
              className="text-gray-400 hover:text-gray-600"
            >
              ···
            </button>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mt-5">
            <Button
              variant={hasApplied ? 'outline' : 'default'}
              className="w-full h-10 rounded-lg text-white cursor-pointer"
              onClick={onApplyJob}
              disabled={!job || applying || hasApplied}
            >
              {applying ? 'Applying…' : hasApplied ? 'Applied' : 'Apply'}
            </Button>
            <Button
              variant="outline"
              className="w-full h-10 rounded-lg cursor-pointer"
              onClick={() => {
                console.log('Save job:', job?.id);
              }}
            >
              Save
            </Button>
          </div>
        </div>

        <div className="p-6 space-y-6">
          <>
            {(showShipDetails || shipType || jobType || genderEquityIndex) && (
              <div className="bg-white rounded-lg p-4 border border-gray-100">
                {showShipDetails && shipImo && (
                  <div className="flex justify-between items-center py-3 border-b border-gray-100">
                    <span className="text-gray-700 font-medium">Ship IMO</span>
                    <span className="text-gray-900 font-semibold">
                      {shipImo}
                    </span>
                  </div>
                )}
                <div className="flex justify-between items-center py-3 border-b border-gray-100">
                  <span className="text-gray-700 font-medium">Ship Type</span>
                  <span className="text-gray-900 font-semibold">
                    {shipType}
                  </span>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-100">
                  <span className="text-gray-700 font-medium">Job Type</span>
                  <span className="text-gray-900 font-semibold">{jobType}</span>
                </div>
                <div className="flex justify-between items-center py-3">
                  <span className="text-gray-700 font-medium">
                    Gender Equity Index
                  </span>
                  <span className="text-gray-900 font-semibold">
                    {genderEquityIndex}
                  </span>
                </div>
              </div>
            )}

            <section>
              <h3 className="text-base font-semibold text-gray-900 mb-2">
                About
              </h3>
              <p className="text-[15px] leading-5 text-gray-700">{aboutText}</p>
            </section>

            <section>
              <h3 className="text-base font-semibold text-gray-900 mb-2">
                Roles and responsibilities
              </h3>
              <div className="text-[15px] leading-5 text-gray-700">
                {rolesResponsibilitiesText &&
                rolesResponsibilitiesText !== 'Not specified' ? (
                  rolesResponsibilitiesText.split('.').map((role, idx) => {
                    const trimmedRole = role.trim();
                    return trimmedRole.length > 0 ? (
                      <div
                        key={`role-${idx}`}
                        className="flex items-start mb-1"
                      >
                        <span className="mr-2">•</span>
                        <span className="flex-1">{trimmedRole}</span>
                      </div>
                    ) : null;
                  })
                ) : (
                  <p>No specific roles and responsibilities listed.</p>
                )}
              </div>
            </section>

            {requirementsText && (
              <section>
                <h3 className="text-base font-semibold text-gray-900 mb-2">
                  Requirements
                </h3>
                <div className="text-[15px] leading-5 text-gray-700">
                  {requirementsText.split('.').map((req, idx) => {
                    const trimmedReq = req.trim();
                    return trimmedReq.length > 0 ? (
                      <div key={`req-${idx}`} className="flex items-start mb-1">
                        <span className="mr-2">•</span>
                        <span className="flex-1">{trimmedReq}</span>
                      </div>
                    ) : null;
                  })}
                </div>
              </section>
            )}

            {benefitsText && (
              <section>
                <h3 className="text-base font-semibold text-gray-900 mb-2">
                  Benefits
                </h3>
                <div className="text-[15px] leading-5 text-gray-700">
                  {benefitsText.split('.').map((benefit, idx) => {
                    const trimmedBenefit = benefit.trim();
                    return trimmedBenefit.length > 0 ? (
                      <div
                        key={`benefit-${idx}`}
                        className="flex items-start mb-1"
                      >
                        <span className="mr-2">•</span>
                        <span className="flex-1">{trimmedBenefit}</span>
                      </div>
                    ) : null;
                  })}
                </div>
              </section>
            )}

            {showAdvancedFields && (
              <div className="space-y-4">
                {certificates.length > 0 && (
                  <Accordion title="Certification">
                    <CertificateTable certificates={certificates} />
                  </Accordion>
                )}

                {experiences.length > 0 && (
                  <Accordion title="Experience">
                    <ExperienceTable experiences={experiences} />
                  </Accordion>
                )}

                {equipments.length > 0 && (
                  <Accordion title="Equipment">
                    <EquipmentTable equipments={equipments} />
                  </Accordion>
                )}

                {documents.length > 0 && (
                  <Accordion title="Document">
                    <DocumentTable documents={documents} />
                  </Accordion>
                )}

                {cargos.length > 0 && (
                  <Accordion title="Cargo">
                    <CargoTable cargos={cargos} />
                  </Accordion>
                )}

                {skills.length > 0 && (
                  <Accordion title="Skills">
                    <SkillsTable skills={skills} />
                  </Accordion>
                )}

                {otherRequirements.length > 0 && (
                  <Accordion title="Other Requirements">
                    <OtherRequirementsTable
                      otherRequirements={otherRequirements}
                    />
                  </Accordion>
                )}

                {benefits && (
                  <Accordion title="Benefits">
                    <BenefitsTable benefits={benefits} />
                  </Accordion>
                )}
              </div>
            )}
          </>
        </div>
      </div>
    </section>
  );
};

const CertificateTable: React.FC<{
  certificates: CertificationRequirementsI[];
}> = ({ certificates }) => (
  <div className="bg-white">
    <div className="flex items-center justify-between p-3 bg-gray-100 rounded-t-lg">
      <span className="font-semibold">Certification</span>
      <span className="font-semibold">Mandatory</span>
    </div>
    {certificates.map((certificate, index) => {
      const certificateMaster = certificate.certificateCourseId ? true : false;
      const isLast = index === certificates.length - 1;

      return (
        <div
          key={index}
          className={`flex items-center justify-between p-3 ${!isLast ? 'border-b border-gray-100' : ''}`}
        >
          <span className="text-gray-800">
            {certificateMaster
              ? certificate.CertificateCourse?.name
              : certificate.CertificateCourseRawData?.name}
          </span>
          <div
            className={`px-3 py-1 rounded-full ${certificate.isMandatory ? 'bg-green-100' : 'bg-gray-100'}`}
          >
            <span
              className={`text-sm font-medium ${certificate.isMandatory ? 'text-green-800' : 'text-gray-600'}`}
            >
              {certificate.isMandatory ? 'Yes' : 'No'}
            </span>
          </div>
        </div>
      );
    })}
  </div>
);

const ExperienceTable: React.FC<{ experiences: ExperienceRequirementsI[] }> = ({
  experiences,
}) => (
  <div className="bg-white p-4">
    <div className="space-y-4">
      {experiences.map((exp, index) => {
        const desigMaster = exp.designationAlternativeId ? true : false;
        const shipTypeMaster = exp.SubVesselType ? true : false;
        const showDesig = exp.isTotal;
        const isLast = index === experiences.length - 1;

        return (
          <div
            key={index}
            className={`py-3 ${!isLast ? 'border-b border-gray-100' : ''}`}
          >
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                {showDesig && (
                  <div className="text-gray-800 font-medium">Designation:</div>
                )}
                <div className="text-gray-800 font-medium">Ship Type:</div>
                <div className="text-gray-800 font-medium">
                  Month(s) of Experience:
                </div>
                <div className="text-gray-800 font-medium">Mandatory:</div>
              </div>
              <div className="space-y-2">
                {showDesig && (
                  <div className="text-gray-800">
                    {desigMaster
                      ? exp.DesignationAlternative?.name
                      : exp.DesignationRawData?.name}
                  </div>
                )}
                <div className="text-gray-800">
                  {shipTypeMaster
                    ? exp.SubVesselType?.name
                    : exp.SubVesselTypeRawData?.name}
                </div>
                <div className="text-gray-800">{exp.monthsOfExperience}</div>
                <div className="text-gray-800">
                  {exp.isMandatory ? 'Yes' : 'No'}
                </div>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  </div>
);

const EquipmentTable: React.FC<{ equipments: EquipmentRequirementsI[] }> = ({
  equipments,
}) => (
  <div className="bg-white p-4">
    <div className="space-y-4">
      {equipments.map((equ, index) => {
        const equCategoryMaster = equ.equipmentCategoryId ? true : false;
        const equModelMaster = equ.equipmentModelId ? true : false;
        const equManufacturerMaster = equ.equipmentManufacturerId
          ? true
          : false;
        const fuelTypeMaster = equ.fuelTypeId ? true : false;
        const isLast = index === equipments.length - 1;

        return (
          <div
            key={index}
            className={`py-3 ${!isLast ? 'border-b border-gray-100' : ''}`}
          >
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="text-gray-800 font-medium">
                  Equipment Category:
                </div>
                <div className="text-gray-800 font-medium">
                  Equipment Manufacturer:
                </div>
                <div className="text-gray-800 font-medium">
                  Equipment Model:
                </div>
                <div className="text-gray-800 font-medium">Fuel type:</div>
                <div className="text-gray-800 font-medium">
                  Month(s) of Experience:
                </div>
                <div className="text-gray-800 font-medium">Mandatory:</div>
              </div>
              <div className="space-y-2">
                <div className="text-gray-800">
                  {equCategoryMaster
                    ? equ.EquipmentCategory?.name
                    : equ.EquipmentCategoryRawData?.name}
                </div>
                <div className="text-gray-800">
                  {equManufacturerMaster
                    ? equ.EquipmentManufacturer?.name
                    : equ.EquipmentManufacturerRawData?.name}
                </div>
                <div className="text-gray-800">
                  {equModelMaster
                    ? equ.EquipmentModel?.name
                    : equ.EquipmentModelRawData?.name}
                </div>
                <div className="text-gray-800">
                  {fuelTypeMaster
                    ? equ.FuelType?.name
                    : equ.FuelTypeRawData?.name}
                </div>
                <div className="text-gray-800">{equ.monthsOfExperience}</div>
                <div className="text-gray-800">
                  {equ.isMandatory ? 'Yes' : 'No'}
                </div>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  </div>
);

const DocumentTable: React.FC<{ documents: DocumentRequirementsI[] }> = ({
  documents,
}) => (
  <div className="bg-white">
    <div className="flex items-center justify-between p-3 bg-gray-100 rounded-t-lg">
      <span className="font-semibold">Document</span>
      <span className="font-semibold">Countries</span>
      <span className="font-semibold">Mandatory</span>
    </div>
    {documents.map((doc, index) => {
      const documentMaster = doc.documentTypeId ? true : false;
      const isLast = index === documents.length - 1;

      return (
        <div
          key={index}
          className={`flex items-center justify-between p-3 ${!isLast ? 'border-b border-gray-100' : ''}`}
        >
          <span className="text-gray-800 flex-1">
            {documentMaster
              ? doc.DocumentType?.name
              : doc.DocumentTypeRawData?.name}
          </span>
          <div className="flex-1 text-center">
            {doc.countries.map((country, idx) => (
              <span key={idx} className="text-gray-800">
                {country}
                {idx < doc.countries.length - 1 ? ', ' : ''}
              </span>
            ))}
          </div>
          <div
            className={`px-3 py-1 rounded-full ${doc.isMandatory ? 'bg-green-100' : 'bg-gray-100'}`}
          >
            <span
              className={`text-sm font-medium ${doc.isMandatory ? 'text-green-800' : 'text-gray-600'}`}
            >
              {doc.isMandatory ? 'Yes' : 'No'}
            </span>
          </div>
        </div>
      );
    })}
  </div>
);

const CargoTable: React.FC<{ cargos: CargosI[] }> = ({ cargos }) => (
  <div className="bg-white">
    <div className="flex items-center justify-between p-3 bg-gray-100 rounded-t-lg">
      <span className="font-semibold">Name</span>
      <span className="font-semibold">Month(s) of Experience</span>
      <span className="font-semibold">Mandatory</span>
    </div>
    {cargos.map((crg, index) => {
      const isLast = index === cargos.length - 1;

      return (
        <div
          key={index}
          className={`flex items-center justify-between p-3 ${!isLast ? 'border-b border-gray-100' : ''}`}
        >
          <span className="text-gray-800 flex-1">{crg.name}</span>
          <span className="text-gray-800 flex-1 text-center">
            {crg.monthsOfExperience}
          </span>
          <div
            className={`px-3 py-1 rounded-full ${crg.isMandatory ? 'bg-green-100' : 'bg-gray-100'}`}
          >
            <span
              className={`text-sm font-medium ${crg.isMandatory ? 'text-green-800' : 'text-gray-600'}`}
            >
              {crg.isMandatory ? 'Yes' : 'No'}
            </span>
          </div>
        </div>
      );
    })}
  </div>
);

const SkillsTable: React.FC<{ skills: SkillRequirementsI[] }> = ({
  skills,
}) => (
  <div className="bg-white">
    <div className="flex items-center justify-between p-3 bg-gray-100 rounded-t-lg">
      <span className="font-semibold">Skill</span>
      <span className="font-semibold">Mandatory</span>
    </div>
    {skills.map((skl, index) => {
      const skillMaster = skl.skillId ? true : false;
      const isLast = index === skills.length - 1;

      return (
        <div
          key={index}
          className={`flex items-center justify-between p-3 ${!isLast ? 'border-b border-gray-100' : ''}`}
        >
          <span className="text-gray-800">
            {skillMaster ? skl.Skill?.name : skl.SkillRawData?.name}
          </span>
          <div
            className={`px-3 py-1 rounded-full ${skl.isMandatory ? 'bg-green-100' : 'bg-gray-100'}`}
          >
            <span
              className={`text-sm font-medium ${skl.isMandatory ? 'text-green-800' : 'text-gray-600'}`}
            >
              {skl.isMandatory ? 'Yes' : 'No'}
            </span>
          </div>
        </div>
      );
    })}
  </div>
);

const OtherRequirementsTable: React.FC<{
  otherRequirements: OtherRequirementsI[];
}> = ({ otherRequirements }) => (
  <div className="bg-white">
    <div className="flex items-center justify-between p-3 bg-gray-100 rounded-t-lg">
      <span className="font-semibold flex-1">Details</span>
      <span className="font-semibold w-20 text-center">Mandatory</span>
    </div>
    {otherRequirements.map((oth, index) => {
      const isLast = index === otherRequirements.length - 1;

      return (
        <div
          key={index}
          className={`flex items-center justify-between p-3 ${!isLast ? 'border-b border-gray-100' : ''}`}
        >
          <span className="text-gray-800 flex-1 pr-4">{oth.details}</span>
          <div
            className={`px-3 py-1 rounded-full ${oth.isMandatory ? 'bg-green-100' : 'bg-gray-100'}`}
          >
            <span
              className={`text-sm font-medium ${oth.isMandatory ? 'text-green-800' : 'text-gray-600'}`}
            >
              {oth.isMandatory ? 'Yes' : 'No'}
            </span>
          </div>
        </div>
      );
    })}
  </div>
);

const BenefitsTable: React.FC<{ benefits: BenefitsI }> = ({ benefits }) => {
  const { internetDetails, insuranceDetails, salaryDetails, contractDetails } =
    benefits;

  const benefitSections = [
    {
      title: 'Internet',
      data: internetDetails,
      entries: Object.entries(internetDetails ?? {}).filter(
        ([_, value]) => value !== undefined && value !== null && value !== ''
      ),
      formatter: (key: string, value: unknown) => {
        if (key === 'internetAvailable')
          return value ? 'Available' : 'Not Available';
        if (key === 'internetSpeed') return `${value} Mbps`;
        if (key === 'internetLimit') return `${value} MB/day`;
        return value as string;
      },
    },
    {
      title: 'Insurance',
      data: insuranceDetails,
      entries: Object.entries(insuranceDetails ?? {}).filter(
        ([_, value]) => value !== undefined && value !== null && value !== ''
      ),
      formatter: (key: string, value: unknown) => {
        if (key === 'familyOnboard') return value ? 'Yes' : 'No';
        return value as string;
      },
    },
    {
      title: 'Salary',
      data: salaryDetails,
      shouldShow: salaryDetails?.showSalary !== false,
      entries: Object.entries(salaryDetails ?? {}).filter(([_key, value]) => {
        if (salaryDetails?.showSalary === false) return false;
        return value !== undefined && value !== null && value !== '';
      }),
      formatter: (key: string, value: unknown) => {
        if (key === 'showSalary') return value ? 'Shown' : 'Hidden';
        if (key === 'minSalary' || key === 'maxSalary') return `${value}`;
        return value as string;
      },
    },
    {
      title: 'Contract',
      data: contractDetails,
      entries: Object.entries(contractDetails ?? {}).filter(
        ([_, value]) => value !== undefined && value !== null
      ),
      formatter: (key: string, value: unknown) => {
        if (key === 'contractDays') return `${value} days`;
        if (key === 'contractMonths') return `${value} months`;
        return value as string;
      },
    },
  ].filter(
    section =>
      section.entries.length > 0 &&
      (section.title !== 'Salary' || section.shouldShow)
  );

  if (benefitSections.length === 0) return null;

  return (
    <div className="bg-white">
      {benefitSections.map((section, sectionIndex) => (
        <div key={section.title} className={sectionIndex > 0 ? 'mt-6' : ''}>
          <div className="font-semibold bg-gray-100 rounded-t-lg p-3 text-center">
            {section.title}
          </div>
          <div className="space-y-0">
            {section.entries.map(([key, value], index) => {
              const isLast = index === section.entries.length - 1;
              const formattedKey = key
                .replace(/([A-Z])/g, ' $1')
                .replace(/^./, str => str.toUpperCase())
                .replace(/(internet|insurance|salary|contract)/gi, '')
                .trim();

              const formattedValue = section.formatter(key, value);

              return (
                <div
                  key={key}
                  className={`flex justify-between items-start p-4 ${!isLast ? 'border-b border-gray-100' : ''}`}
                >
                  <span className="text-gray-800 font-medium flex-1 mr-3">
                    {formattedKey}:
                  </span>
                  <span className="text-gray-600 flex-1 text-right">
                    {formattedValue}
                  </span>
                </div>
              );
            })}
          </div>
        </div>
      ))}
    </div>
  );
};

export default JobDetail;
