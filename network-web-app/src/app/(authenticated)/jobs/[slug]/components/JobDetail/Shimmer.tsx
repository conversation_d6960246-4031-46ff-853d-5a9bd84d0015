'use client';

import React from 'react';

type ShimmerPropsI = {
  className?: string;
};

const Shimmer = ({ className = '' }: ShimmerPropsI) => (
  <div className={`animate-pulse bg-gray-200 rounded ${className}`} />
);

export const JobDetailSkeleton = () => (
  <section className="lg:col-span-6 col-span-1">
    <div className="bg-white border border-gray-200 rounded-2xl shadow-sm">
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-start justify-between gap-4">
          <div className="flex-1">
            <div className="flex items-center mb-2">
              <Shimmer className="h-4 w-4 rounded-full mr-2" />
              <Shimmer className="h-4 w-32" />
            </div>

            <Shimmer className="h-8 w-3/4 mb-2" />

            <div className="flex items-center gap-2 mb-2">
              <Shimmer className="h-4 w-24" />
              <Shimmer className="h-1 w-1 rounded-full" />
              <Shimmer className="h-4 w-20" />
              <Shimmer className="h-1 w-1 rounded-full" />
              <Shimmer className="h-6 w-20 rounded-full" />
            </div>

            <Shimmer className="h-6 w-16 rounded-full" />
          </div>

          <Shimmer className="w-6 h-6" />
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mt-5">
          <Shimmer className="w-full h-10 rounded-lg" />
          <Shimmer className="w-full h-10 rounded-lg" />
        </div>
      </div>

      <div className="p-6 space-y-6">
        <div className="bg-white rounded-lg p-4 border border-gray-100">
          <div className="space-y-3">
            <div className="flex justify-between items-center py-3 border-b border-gray-100">
              <Shimmer className="h-4 w-20" />
              <Shimmer className="h-4 w-24" />
            </div>
            <div className="flex justify-between items-center py-3 border-b border-gray-100">
              <Shimmer className="h-4 w-16" />
              <Shimmer className="h-4 w-32" />
            </div>
            <div className="flex justify-between items-center py-3 border-b border-gray-100">
              <Shimmer className="h-4 w-18" />
              <Shimmer className="h-4 w-28" />
            </div>
            <div className="flex justify-between items-center py-3">
              <Shimmer className="h-4 w-32" />
              <Shimmer className="h-4 w-8" />
            </div>
          </div>
        </div>

        <section>
          <Shimmer className="h-6 w-16 mb-2" />
          <div className="space-y-2">
            <Shimmer className="h-4 w-full" />
            <Shimmer className="h-4 w-5/6" />
            <Shimmer className="h-4 w-4/5" />
          </div>
        </section>

        <section>
          <Shimmer className="h-6 w-40 mb-2" />
          <div className="space-y-2">
            <div className="flex items-start">
              <Shimmer className="h-2 w-2 rounded-full mr-2 mt-2" />
              <Shimmer className="h-4 w-3/4" />
            </div>
            <div className="flex items-start">
              <Shimmer className="h-2 w-2 rounded-full mr-2 mt-2" />
              <Shimmer className="h-4 w-5/6" />
            </div>
            <div className="flex items-start">
              <Shimmer className="h-2 w-2 rounded-full mr-2 mt-2" />
              <Shimmer className="h-4 w-2/3" />
            </div>
          </div>
        </section>

        <section>
          <Shimmer className="h-6 w-24 mb-2" />
          <div className="space-y-2">
            <div className="flex items-start">
              <Shimmer className="h-2 w-2 rounded-full mr-2 mt-2" />
              <Shimmer className="h-4 w-4/5" />
            </div>
            <div className="flex items-start">
              <Shimmer className="h-2 w-2 rounded-full mr-2 mt-2" />
              <Shimmer className="h-4 w-3/4" />
            </div>
            <div className="flex items-start">
              <Shimmer className="h-2 w-2 rounded-full mr-2 mt-2" />
              <Shimmer className="h-4 w-5/6" />
            </div>
          </div>
        </section>

        <div className="space-y-4">
          <div className="border border-gray-200 rounded-lg">
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-t-lg">
              <Shimmer className="h-5 w-24" />
              <Shimmer className="h-5 w-5" />
            </div>
          </div>

          <div className="border border-gray-200 rounded-lg">
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-t-lg">
              <Shimmer className="h-5 w-20" />
              <Shimmer className="h-5 w-5" />
            </div>
          </div>

          <div className="border border-gray-200 rounded-lg">
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-t-lg">
              <Shimmer className="h-5 w-18" />
              <Shimmer className="h-5 w-5" />
            </div>
          </div>

          <div className="border border-gray-200 rounded-lg">
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-t-lg">
              <Shimmer className="h-5 w-20" />
              <Shimmer className="h-5 w-5" />
            </div>
          </div>

          <div className="border border-gray-200 rounded-lg">
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-t-lg">
              <Shimmer className="h-5 w-12" />
              <Shimmer className="h-5 w-5" />
            </div>
          </div>

          <div className="border border-gray-200 rounded-lg">
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-t-lg">
              <Shimmer className="h-5 w-16" />
              <Shimmer className="h-5 w-5" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
);

export default JobDetailSkeleton;
