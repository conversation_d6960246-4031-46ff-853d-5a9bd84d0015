'use client';

import { useParams } from 'next/navigation';
import DashboardLayout from '../../../forums/components/DashboardLayout';
import ApplicantsList from './components/ApplicantsList';

const ApplicantsPage = () => {
  const { slug } = useParams<{ slug: string }>();
  return (
    <DashboardLayout>
      <ApplicantsList jobId={slug} />
    </DashboardLayout>
  );
};

export default ApplicantsPage;
