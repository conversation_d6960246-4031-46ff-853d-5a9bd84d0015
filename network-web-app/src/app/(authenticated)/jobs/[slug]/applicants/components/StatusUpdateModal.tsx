'use client';

import React, { useState } from 'react';
import { Button } from '@/components';
import { ApplicationStatusUpdateI } from '@/networks/jobs/types';

export type StatusUpdateModalProps = {
  isOpen: boolean;
  onClose: () => void;
  onUpdate: (status: ApplicationStatusUpdateI) => Promise<void>;
  applicantName: string;
  currentStatus?: string;
};

const STATUS_OPTIONS = [
  {
    key: 'SHORTLISTED' as ApplicationStatusUpdateI,
    label: 'Shortlist',
    description: 'Move to shortlisted candidates',
    className: 'text-blue-600 hover:bg-blue-50 border-blue-200',
  },
  {
    key: 'REJECTED_BY_ENTITY' as ApplicationStatusUpdateI,
    label: 'Reject',
    description: 'Reject this application',
    className: 'text-red-600 hover:bg-red-50 border-red-200',
  },
  {
    key: 'OFFERED' as ApplicationStatusUpdateI,
    label: 'Offer',
    description: 'Send job offer to candidate',
    className: 'text-green-600 hover:bg-green-50 border-green-200',
  },
] as const;

const StatusUpdateModal: React.FC<StatusUpdateModalProps> = ({
  isOpen,
  onClose,
  onUpdate,
  applicantName,
  currentStatus,
}) => {
  const [selectedStatus, setSelectedStatus] =
    useState<ApplicationStatusUpdateI | null>(null);
  const [updating, setUpdating] = useState(false);

  if (!isOpen) return null;

  const handleStatusUpdate = async () => {
    if (!selectedStatus) return;

    try {
      setUpdating(true);
      await onUpdate(selectedStatus);
      onClose();
      setSelectedStatus(null);
    } catch (error) {
      console.error('Failed to update status:', error);
    } finally {
      setUpdating(false);
    }
  };

  const handleClose = () => {
    if (updating) return;
    setSelectedStatus(null);
    onClose();
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div
        className="absolute inset-0 bg-black opacity-55"
        onClick={handleClose}
      />

      {/* Modal */}
      <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">
              Update Application Status
            </h3>
            <button
              onClick={handleClose}
              disabled={updating}
              className="text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50"
            >
              <svg
                className="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
          <p className="text-sm text-gray-600 mt-1">
            Update status for{' '}
            <span className="font-medium">{applicantName}</span>
          </p>
          {currentStatus && (
            <p className="text-xs text-gray-500 mt-1">
              Current status:{' '}
              <span className="font-medium">{currentStatus}</span>
            </p>
          )}
        </div>

        {/* Content */}
        <div className="px-6 py-4">
          <div className="space-y-3">
            {STATUS_OPTIONS.map(option => (
              <button
                key={option.key}
                onClick={() => setSelectedStatus(option.key)}
                disabled={updating}
                className={`w-full p-4 text-left border-2 rounded-lg transition-all disabled:opacity-50 ${
                  selectedStatus === option.key
                    ? `${option.className} border-opacity-100`
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div>
                    <div
                      className={`font-medium ${
                        selectedStatus === option.key
                          ? option.className.split(' ')[0]
                          : 'text-gray-900'
                      }`}
                    >
                      {option.label}
                    </div>
                    <div className="text-sm text-gray-600 mt-1">
                      {option.description}
                    </div>
                  </div>
                  {selectedStatus === option.key && (
                    <div
                      className={`w-5 h-5 rounded-full border-2 ${option.className.split(' ')[0]} ${option.className.split(' ')[0].replace('text-', 'border-')} flex items-center justify-center`}
                    >
                      <div
                        className={`w-2 h-2 rounded-full ${option.className.split(' ')[0].replace('text-', 'bg-')}`}
                      />
                    </div>
                  )}
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-gray-200 flex gap-3">
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={updating}
            className="flex-1"
          >
            Cancel
          </Button>
          <Button
            onClick={handleStatusUpdate}
            disabled={!selectedStatus || updating}
            className="flex-1 text-white"
          >
            {updating ? (
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                Updating...
              </div>
            ) : (
              'Update Status'
            )}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default StatusUpdateModal;
