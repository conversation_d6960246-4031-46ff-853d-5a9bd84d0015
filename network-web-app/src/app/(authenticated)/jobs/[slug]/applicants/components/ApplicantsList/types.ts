import {
  ApplicantsStatusI,
  FilterItemI,
  FilterTabI,
} from '@/networks/jobs/types';

export type UseApplicantFiltersProps = {
  jobId: string;
  activeTab: ApplicantsStatusI;
};

export type UseApplicantFiltersReturn = {
  filterTabs: FilterTabI[];
  filterData: Record<string, FilterItemI[]>;
  selectedFilters: Record<string, FilterItemI[]>;
  loading: boolean;
  error: string | null;
  isFilterModalOpen: boolean;
  hasActiveFilters: boolean;
  activeFilterCount: number;
  isApplyingFilters: boolean;
  openFilterModal: () => void;
  closeFilterModal: () => void;
  applyFilters: (
    filters: Record<string, FilterItemI[]>,
    shouldRefetch?: boolean
  ) => boolean;
  clearAllFilters: (shouldRefetch?: boolean) => boolean;
  loadFilters: () => Promise<void>;
  getFilteredPayload: () => any;
};
