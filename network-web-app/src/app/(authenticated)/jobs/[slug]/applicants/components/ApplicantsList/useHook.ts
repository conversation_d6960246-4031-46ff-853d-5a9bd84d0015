import { useState, useCallback, useEffect } from 'react';
import { fetchFiltersForApplicantsAPI } from '@/networks/jobs';
import { UseApplicantFiltersProps, UseApplicantFiltersReturn } from './types';
import { FilterItemI, FilterTabI } from '@/networks/jobs/types';

const useApplicantFilters = ({
  jobId,
  activeTab,
}: UseApplicantFiltersProps): UseApplicantFiltersReturn => {
  const [filterData, setFilterData] = useState<Record<string, FilterItemI[]>>(
    {}
  );
  const [selectedFilters, setSelectedFilters] = useState<
    Record<string, FilterItemI[]>
  >({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);
  const [isApplyingFilters, setIsApplyingFilters] = useState(false);

  const filterTabs: FilterTabI[] = [
    {
      id: 'locations',
      label: 'Locations',
      appliedCount: selectedFilters.locations?.length || 0,
    },
    {
      id: 'designations',
      label: 'Designations',
      appliedCount: selectedFilters.designations?.length || 0,
    },
    {
      id: 'yearsOfExperiences',
      label: 'Experience',
      appliedCount: selectedFilters.yearsOfExperiences?.length || 0,
    },
  ];

  const loadFilters = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const filtersResponse = await fetchFiltersForApplicantsAPI({
        jobId,
        status: activeTab,
      });

      setFilterData({
        locations: filtersResponse.locations || [],
        designations: filtersResponse.designations || [],
        yearsOfExperiences: filtersResponse.yearsOfExperiences || [],
      });
    } catch (err) {
      console.error('Failed to load filters:', err);
      setError('Failed to load filters. Please try again.');
    } finally {
      setLoading(false);
    }
  }, [jobId, activeTab]);

  const openFilterModal = useCallback(() => {
    setIsFilterModalOpen(true);
    if (Object.keys(filterData).length === 0) {
      loadFilters();
    }
  }, [filterData, loadFilters]);

  const closeFilterModal = useCallback(() => {
    setIsFilterModalOpen(false);
  }, []);

  const applyFilters = useCallback(
    (filters: Record<string, FilterItemI[]>, shouldRefetch = true) => {
      if (shouldRefetch) {
        setIsApplyingFilters(true);
        setTimeout(() => setIsApplyingFilters(false), 1000);
      }
      setSelectedFilters(filters);
      setIsFilterModalOpen(false);
      return shouldRefetch;
    },
    []
  );

  const clearAllFilters = useCallback((shouldRefetch = true) => {
    if (shouldRefetch) {
      setIsApplyingFilters(true);
      setTimeout(() => setIsApplyingFilters(false), 1000);
    }
    setSelectedFilters({});
    return shouldRefetch;
  }, []);

  const hasActiveFilters = Object.values(selectedFilters).some(
    filters => filters.length > 0
  );

  const activeFilterCount = Object.values(selectedFilters).reduce(
    (total, filters) => total + filters.length,
    0
  );

  const getFilteredPayload = useCallback(() => {
    return {
      designations:
        selectedFilters.designations?.length > 0
          ? selectedFilters.designations.map(d => ({
              id: d.id,
              dataType: d.dataType || 'master',
            }))
          : null,
      countries:
        selectedFilters.locations?.length > 0
          ? selectedFilters.locations.map(c => c.id)
          : null,
      yearsOfExperiences:
        selectedFilters.yearsOfExperiences?.length > 0
          ? selectedFilters.yearsOfExperiences
          : null,
    };
  }, [selectedFilters]);

  useEffect(() => {
    if (jobId) {
      setFilterData({});
      setSelectedFilters({});
    }
  }, [jobId, activeTab]);

  return {
    filterTabs,
    filterData,
    selectedFilters,
    loading,
    error,
    isFilterModalOpen,
    hasActiveFilters,
    activeFilterCount,
    isApplyingFilters,
    openFilterModal,
    closeFilterModal,
    applyFilters,
    clearAllFilters,
    loadFilters,
    getFilteredPayload,
  };
};

export default useApplicantFilters;
