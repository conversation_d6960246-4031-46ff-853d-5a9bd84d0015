import { Tabs } from '@/components';
import JobFilterBar from '@/components/JobFilterBar';
import {
  fetchApplicantsForJobAPI,
  updateApplicationStatusAPI,
} from '@/networks/jobs';
import {
  ApplicantsStatusI,
  FetchApplicantsBodyI,
  FetchApplicantsResultItemI,
  ApplicationStatusUpdateI,
} from '@/networks/jobs/types';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import useApplicantFilters from './useHook';
import StatusUpdateModal from '../StatusUpdateModal';
import Edit from '@assets/svg/Edit';

const tabs = [
  { id: 'PENDING', label: 'Applied' },
  { id: 'SHORTLISTED', label: 'Shortlisted' },
  { id: 'ACCEPTED_BY_APPLICANT', label: 'Accepted' },
  { id: 'REJECTED_BY_ENTITY', label: 'Rejected' },
  { id: 'OFFERED', label: 'Offered' },
];

const ApplicantsList = ({ jobId }: { jobId: string }) => {
  const [activeTab, setActiveTab] = useState<ApplicantsStatusI>('PENDING');
  const [search, setSearch] = useState('');

  const [items, setItems] = useState<FetchApplicantsResultItemI[]>([]);
  const [nextCursorId, setNextCursorId] = useState<number | null>(null);
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const loadMoreRef = useRef<HTMLDivElement>(null);

  // Status update modal state
  const [statusModalOpen, setStatusModalOpen] = useState(false);
  const [selectedApplicant, setSelectedApplicant] =
    useState<FetchApplicantsResultItemI | null>(null);

  const {
    filterData,
    selectedFilters,
    loading: filterLoading,
    filterTabs,
    applyFilters,
    clearAllFilters,
    hasActiveFilters,
    activeFilterCount,
    isApplyingFilters,
    getFilteredPayload,
    isFilterModalOpen,
    openFilterModal,
    closeFilterModal,
  } = useApplicantFilters({
    jobId,
    activeTab,
  });

  const fetchApplicants = useCallback(
    async (isLoadMore = false) => {
      try {
        if (!jobId) return;
        if (isLoadMore) {
          if (!hasMore || !nextCursorId) return;
          setLoadingMore(true);
        } else {
          setLoading(true);
        }

        const payload: FetchApplicantsBodyI = getFilteredPayload();

        const res = await fetchApplicantsForJobAPI(
          {
            jobId,
            status: activeTab,
            cursorId: isLoadMore && nextCursorId ? String(nextCursorId) : null,
            pageSize: 10,
          },
          payload
        );

        setItems(prev => (isLoadMore ? [...prev, ...res.data] : res.data));
        setNextCursorId(res.nextCursorId);
        setHasMore(!!res.nextCursorId && res.data.length > 0);
      } catch (e) {
        console.error('Failed to fetch applicants', e);
      } finally {
        setLoading(false);
        setLoadingMore(false);
      }
    },
    [jobId, activeTab, nextCursorId, hasMore, getFilteredPayload]
  );

  useEffect(() => {
    setItems([]);
    setNextCursorId(null);
    setHasMore(true);
    fetchApplicants(false);
  }, [activeTab, selectedFilters]);

  const handleApplyFilters = useCallback(
    (filters: Record<string, any>) => {
      applyFilters(filters, true);
      setItems([]);
      setNextCursorId(null);
      setHasMore(true);
      fetchApplicants(false);
    },
    [applyFilters, fetchApplicants]
  );

  const handleClearFilters = useCallback(() => {
    clearAllFilters(true);
    setItems([]);
    setNextCursorId(null);
    setHasMore(true);
    fetchApplicants(false);
  }, [clearAllFilters, fetchApplicants]);

  // Status update handlers
  const handleEditClick = useCallback(
    (applicant: FetchApplicantsResultItemI) => {
      setSelectedApplicant(applicant);
      setStatusModalOpen(true);
    },
    []
  );

  const handleStatusUpdate = useCallback(
    async (status: ApplicationStatusUpdateI) => {
      if (!selectedApplicant) return;

      try {
        await updateApplicationStatusAPI(selectedApplicant.id, status);

        setItems(prev => prev.filter(item => item.id !== selectedApplicant.id));

        console.log('Status updated successfully');
      } catch (error) {
        console.error('Failed to update status:', error);
      }
    },
    [selectedApplicant]
  );

  const handleCloseStatusModal = useCallback(() => {
    setStatusModalOpen(false);
    setSelectedApplicant(null);
  }, []);

  useEffect(() => {
    const el = loadMoreRef.current;
    if (!el) return;
    const observer = new IntersectionObserver(
      entries => {
        const [entry] = entries;
        if (entry.isIntersecting && hasMore && !loading && !loadingMore) {
          fetchApplicants(true);
        }
      },
      { root: null, rootMargin: '100px', threshold: 0.1 }
    );
    observer.observe(el);
    return () => observer.disconnect();
  }, [hasMore, loading, loadingMore]);

  const filtered = useMemo(() => {
    const s = search.trim().toLowerCase();
    const source = items.map(a => ({
      id: a.id,
      name: a.ApplicantProfile?.name || '—',
      title: a.DecisionMakerProfile?.name || '',
      avatar: a.ApplicantProfile?.avatar || null,
    }));
    if (!s) return source;
    return source.filter(
      a => a.name.toLowerCase().includes(s) || a.title.toLowerCase().includes(s)
    );
  }, [items, search]);

  return (
    <section className="lg:col-span-6 col-span-1">
      <div className="bg-white border border-gray-200 rounded-2xl shadow-sm">
        <div className="px-4 pt-4">
          <Tabs
            items={tabs}
            activeId={activeTab}
            onChange={id => setActiveTab(id as ApplicantsStatusI)}
          />
        </div>

        {/* Filter Bar */}
        <div className="px-4 py-3">
          <JobFilterBar
            search={search}
            onSearchChange={setSearch}
            isFilterModalOpen={isFilterModalOpen}
            onOpenFilterModal={openFilterModal}
            onCloseFilterModal={closeFilterModal}
            onApplyFilters={handleApplyFilters}
            onClearAllFilters={handleClearFilters}
            filterTabs={filterTabs}
            filterData={filterData}
            currentFilters={selectedFilters}
            filterLoading={filterLoading}
            hasActiveFilters={hasActiveFilters}
            activeFilterCount={activeFilterCount}
            isApplyingFilters={isApplyingFilters}
          />
        </div>

        {/* List */}
        {loading && items.length === 0 ? (
          <div className="px-4 py-12 text-center text-gray-500">Loading…</div>
        ) : filtered.length === 0 ? (
          <div className="px-4 py-12 text-center text-gray-500">
            No results found
          </div>
        ) : (
          <ul className="divide-y divide-gray-200">
            {filtered.map(a => {
              const originalApplicant = items.find(item => item.id === a.id);
              return (
                <li key={`${activeTab}-${a.id}`} className="px-4">
                  <div className="flex gap-4 py-4">
                    <div className="h-8 w-8 rounded-full overflow-hidden flex-shrink-0 bg-gray-100" />
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-semibold text-gray-900">
                        {a.name}
                      </p>
                      <p className="text-sm text-gray-700">{a.title}</p>
                    </div>
                    <div className="flex items-center">
                      <button
                        onClick={() =>
                          originalApplicant &&
                          handleEditClick(originalApplicant)
                        }
                        className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                        title="Edit application status"
                      >
                        <Edit width={16} height={16} />
                      </button>
                    </div>
                  </div>
                </li>
              );
            })}
          </ul>
        )}

        <div ref={loadMoreRef} />
      </div>

      {/* Status Update Modal */}
      <StatusUpdateModal
        isOpen={statusModalOpen}
        onClose={handleCloseStatusModal}
        onUpdate={handleStatusUpdate}
        applicantName={selectedApplicant?.ApplicantProfile?.name || ''}
        currentStatus={selectedApplicant?.status}
      />
    </section>
  );
};

export default ApplicantsList;
